<template>
  <v-form @submit.prevent>
    <v-container>
      <v-alert
        closable
        title="Opslaan gelukt"
        type="success"
        v-if="saveSuccess"
      ></v-alert>
      <v-alert
        closable
        title="Opslaan mislukt"
        :text="saveError"
        type="error"
        v-if="saveError"
      ></v-alert>
      <v-card-text class="text-caption">
        <PERSON><PERSON> hier welk audio apparaat de input moet leveren voor de transcriptie
      </v-card-text>
      <v-autocomplete
        label="Audio"
        :items="devices"
        v-model="device"
        :menu-props="{ eager: true }"
      ></v-autocomplete>
      <v-card-text class="text-caption">
        Minimaal volume in RMS van de microfoon voordat er spraak opgenomen
        wordt
      </v-card-text>
      <v-slider
        v-model="minMicVolume"
        :max="1000"
        :min="0"
        :step="1"
        class="align-center"
        hide-details
      >
        <template v-slot:append>
          <v-text-field
            v-model="minMicVolume"
            density="compact"
            style="width: 100px"
            type="number"
            hide-details
            single-line
          ></v-text-field>
        </template>
      </v-slider>
      <v-card-text class="text-caption">
        Minimaal volume in dB van het segment voordat het verwerkt wordt
      </v-card-text>
      <v-slider
        v-model="minSegVolumeDb"
        :max="200"
        :min="0"
        :step="1"
        class="align-center"
        hide-details
      >
        <template v-slot:append>
          <v-text-field
            v-model="minSegVolumeDb"
            density="compact"
            style="width: 100px"
            type="number"
            hide-details
            single-line
          ></v-text-field>
        </template>
      </v-slider>
      <v-card-text class="text-caption"> Taal van de preek </v-card-text>
      <v-autocomplete
        label="Talen"
        :items="languages"
        item-title="name"
        item-value="code"
        v-model="language"
        :menu-props="{ eager: true }"
      ></v-autocomplete>
      <v-card-text class="text-caption">
        Minimale en maximale tijd van een tekstfragment
      </v-card-text>
      <v-range-slider
        v-model="minAndMaxSeconds"
        strict
        :min="0"
        :max="30"
        :step="1"
      >
        <template v-slot:prepend>
          <v-text-field
            v-model="minAndMaxSeconds[0]"
            density="compact"
            style="width: 70px"
            type="number"
            variant="outlined"
            hide-details
            single-line
          ></v-text-field>
        </template>
        <template v-slot:append>
          <v-text-field
            v-model="minAndMaxSeconds[1]"
            density="compact"
            style="width: 70px"
            type="number"
            variant="outlined"
            hide-details
            single-line
          ></v-text-field> </template
      ></v-range-slider>
      <v-card-text class="text-caption">
        Behulpzame woorden die de tekst verbeteren.
      </v-card-text>
      <v-combobox
        label="Veel voorkomende woorden"
        v-model="commonWords"
        chips
        multiple
      ></v-combobox>
      <v-card-text class="text-caption">
        Wanneer deze woorden in een zin staan, wordt de hele zin verwijderd.
      </v-card-text>
      <v-combobox
        label="Genegeerde woorden"
        v-model="ignoredWords"
        chips
        multiple
      ></v-combobox>
      <div class="d-flex">
        <v-checkbox v-model="enableVad" label="VAD inschakelen"></v-checkbox>
        <v-checkbox
          v-model="saveFile"
          label="Audiobestand opslaan"
        ></v-checkbox>
        <v-checkbox
          v-model="saveTranslationCsvLog"
          label="Transcriptie als CSV opslaan"
        ></v-checkbox>
        <v-checkbox
          v-model="finalTranscriptionOnly"
          label="Alleen definitieve transcriptie"
          hint="Toon alleen de verbeterde, definitieve transcriptie (langzamer maar nauwkeuriger)"
        ></v-checkbox>
      </div>

      <!-- Auto Restart Settings -->
      <v-divider class="my-6"></v-divider>
      <v-card-title class="text-h6 pa-0 mb-4">Automatisch Herstarten</v-card-title>
      <v-card-text class="text-caption pa-0">
        Stel in wanneer het apparaat automatisch moet herstarten
      </v-card-text>

      <v-checkbox
        v-model="autoRestartEnabled"
        label="Automatisch herstarten inschakelen"
        class="mb-4"
      ></v-checkbox>

      <div v-if="autoRestartEnabled" class="ml-6">
        <v-card-text class="text-caption pa-0">Tijd voor herstart</v-card-text>
        <v-text-field
          v-model="autoRestartTime"
          label="Herstart tijd (HH:MM)"
          type="time"
          density="compact"
          style="max-width: 200px"
          class="mb-4"
        ></v-text-field>

        <v-card-text class="text-caption pa-0">Dagen van de week</v-card-text>
        <v-select
          v-model="autoRestartDays"
          :items="['maandag', 'dinsdag', 'woensdag', 'donderdag', 'vrijdag', 'zaterdag', 'zondag']"
          label="Selecteer dagen"
          multiple
          chips
          density="compact"
          class="mb-4"
        ></v-select>
      </div>

      <!-- Auto Upload Settings -->
      <v-divider class="my-6"></v-divider>
      <v-card-title class="text-h6 pa-0 mb-4">Automatische Upload naar Preeklezen</v-card-title>
      <v-card-text class="text-caption pa-0">
        Stel in wanneer de meest recente opname automatisch wordt geüpload naar Preeklezen
      </v-card-text>

      <v-checkbox
        v-model="autoUploadEnabled"
        label="Automatische upload inschakelen"
        class="mb-4"
      ></v-checkbox>

      <div v-if="autoUploadEnabled" class="ml-6">
        <v-card-text class="text-caption pa-0 mb-2">Upload schema's</v-card-text>

        <div v-for="(schedule, index) in autoUploadSchedules" :key="index" class="mb-4 pa-4 border rounded">
          <div class="d-flex align-center mb-2">
            <v-checkbox
              v-model="schedule.enabled"
              :label="`Schema ${index + 1}`"
              density="compact"
            ></v-checkbox>
            <v-spacer></v-spacer>
            <v-btn
              icon="mdi-delete"
              size="small"
              variant="text"
              @click="removeUploadSchedule(index)"
              v-if="autoUploadSchedules.length > 1"
            ></v-btn>
          </div>

          <div v-if="schedule.enabled" class="ml-6">
            <div class="d-flex gap-4">
              <v-select
                v-model="schedule.day"
                :items="['maandag', 'dinsdag', 'woensdag', 'donderdag', 'vrijdag', 'zaterdag', 'zondag']"
                label="Dag"
                density="compact"
                style="max-width: 150px"
              ></v-select>

              <v-text-field
                v-model="schedule.time"
                label="Tijd (HH:MM)"
                type="time"
                density="compact"
                style="max-width: 150px"
              ></v-text-field>
            </div>
          </div>
        </div>

        <v-btn
          @click="addUploadSchedule"
          variant="outlined"
          size="small"
          prepend-icon="mdi-plus"
        >
          Schema toevoegen
        </v-btn>
      </div>

      <v-divider class="my-6"></v-divider>
      <div class="d-flex">
        <v-btn @click="reset()"> Reset </v-btn>
        <v-spacer></v-spacer>
        <v-btn color="primary" type="submit" @click="send()"> Verzenden </v-btn>
      </div>
    </v-container>
  </v-form>
</template>

<script setup>
import languages from "@/assets/languages.json";

definePageMeta({
  title: "Instellingen",
  layout: "secure",
});

const settingsStore = useSettingsStore();

const device = ref(settingsStore.settings["alsaInput"]);
const language = ref(settingsStore.settings["language"]);
const minMicVolume = ref(settingsStore.settings["minMicVolume"]);
const minSegVolumeDb = ref(settingsStore.settings["minSegVolumeDb"]);
const commonWords = ref(settingsStore.settings["commonWords"]);
const ignoredWords = ref(settingsStore.settings["ignoredWords"]);
const minAndMaxSeconds = ref([
  settingsStore.settings["minStepS"],
  settingsStore.settings["maxStepS"],
]);
const enableVad = ref(settingsStore.settings["enableVad"]);
const saveFile = ref(settingsStore.settings["saveFile"]);
const saveTranslationCsvLog = ref(
  settingsStore.settings["saveTranslationCsvLog"]
);
const finalTranscriptionOnly = ref(
  settingsStore.settings["finalTranscriptionOnly"]
);

// New settings for auto restart and upload
const autoRestartEnabled = ref(settingsStore.settings["autoRestartEnabled"] || false);
const autoRestartTime = ref(settingsStore.settings["autoRestartTime"] || "03:00");
const autoRestartDays = ref(settingsStore.settings["autoRestartDays"] || []);
const autoUploadEnabled = ref(settingsStore.settings["autoUploadEnabled"] || false);
const autoUploadSchedules = ref(settingsStore.settings["autoUploadSchedules"] || [
  { day: "zondag", time: "12:00", enabled: false },
  { day: "zondag", time: "20:00", enabled: false }
]);

const devices = await $fetch(`http://${location.hostname}:5001/devices`);

function reset() {
  device.value = settingsStore.settings["alsaInput"];
  language.value = settingsStore.settings["language"];
  minMicVolume.value = settingsStore.settings["minMicVolume"];
  minSegVolumeDb.value = settingsStore.settings["minSegVolumeDb"];
  commonWords.value = settingsStore.settings["commonWords"];
  ignoredWords.value = settingsStore.settings["ignoredWords"];
  minAndMaxSeconds.value = [
    settingsStore.settings["minStepS"],
    settingsStore.settings["maxStepS"],
  ];
  enableVad.value = settingsStore.settings["enableVad"];
  saveFile.value = settingsStore.settings["saveFile"];
  saveTranslationCsvLog.value = settingsStore.settings["saveTranslationCsvLog"];
  finalTranscriptionOnly.value =
    settingsStore.settings["finalTranscriptionOnly"];
  autoRestartEnabled.value = settingsStore.settings["autoRestartEnabled"] || false;
  autoRestartTime.value = settingsStore.settings["autoRestartTime"] || "03:00";
  autoRestartDays.value = settingsStore.settings["autoRestartDays"] || [];
  autoUploadEnabled.value = settingsStore.settings["autoUploadEnabled"] || false;
  autoUploadSchedules.value = settingsStore.settings["autoUploadSchedules"] || [
    { day: "zondag", time: "12:00", enabled: false },
    { day: "zondag", time: "20:00", enabled: false }
  ];
}

const saveSuccess = ref(false);
const saveError = ref(false);

async function send() {
  try {
    saveSuccess.value = false;
    saveError.value = false;
    settingsStore.settings = await $fetch(
      `http://${location.hostname}:5001/settings`,
      {
        method: "PATCH",
        body: {
          alsaInput: device.value,
          minMicVolume: minMicVolume.value,
          minSegVolumeDb: minSegVolumeDb.value,
          language: language.value,
          commonWords: commonWords.value,
          ignoredWords: ignoredWords.value,
          minStepS: minAndMaxSeconds.value[0],
          maxStepS: minAndMaxSeconds.value[1],
          enableVad: enableVad.value,
          saveFile: saveFile.value,
          saveTranslationCsvLog: saveTranslationCsvLog.value,
          finalTranscriptionOnly: finalTranscriptionOnly.value,
          autoRestartEnabled: autoRestartEnabled.value,
          autoRestartTime: autoRestartTime.value,
          autoRestartDays: autoRestartDays.value,
          autoUploadEnabled: autoUploadEnabled.value,
          autoUploadSchedules: autoUploadSchedules.value,
        },
      }
    );

    saveSuccess.value = true;
  } catch (e) {
    saveError.value = true;
  }
}

function addUploadSchedule() {
  autoUploadSchedules.value.push({
    day: "zondag",
    time: "12:00",
    enabled: false
  });
}

function removeUploadSchedule(index) {
  if (autoUploadSchedules.value.length > 1) {
    autoUploadSchedules.value.splice(index, 1);
  }
}
</script>
