package internal

import (
	"bufio"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"

	"github.com/google/uuid"
)

type UploadSchedule struct {
	Day     string `json:"day"`
	Time    string `json:"time"`
	Enabled bool   `json:"enabled"`
}

type Settings struct {
	OpenAIApiKey           string           `json:"openAIApiKey"`
	AlsaInput              string           `json:"alsaInput"`
	MinMicVolume           int              `json:"minMicVolume"`
	MinSegVolumeDb         int              `json:"minSegVolumeDb"`
	MinStepS               int              `json:"minStepS"`
	MaxStepS               int              `json:"maxStepS"`
	IgnoredWords           []string         `json:"ignoredWords"`
	CommonWords            []string         `json:"commonWords"`
	Language               string           `json:"language"`
	PostProcessingPrompt   string           `json:"postProcessingPrompt"`
	EnableVad              bool             `json:"enableVad"`
	SaveFile               bool             `json:"saveFile"`
	WhisperVersion         string           `json:"whisperVersion"`
	ChatGPTVersion         string           `json:"chatGPTVersion"`
	SetupFinished          bool             `json:"setupFinished"`
	SaveTranslationCsvLog  bool             `json:"saveTranslationCsvLog"`
	DevMode                bool             `json:"devMode"`
	PreektekstApiUrl       string           `json:"preektekstApiUrl"`
	PreektekstApiKey       string           `json:"preektekstApiKey"`
	PreektekstChurchID     uint             `json:"churchID"`
	FinalTranscriptionOnly bool             `json:"finalTranscriptionOnly"`
	AutoRestartEnabled     bool             `json:"autoRestartEnabled"`
	AutoRestartTime        string           `json:"autoRestartTime"`
	AutoRestartDays        []string         `json:"autoRestartDays"`
	AutoUploadEnabled      bool             `json:"autoUploadEnabled"`
	AutoUploadSchedules    []UploadSchedule `json:"autoUploadSchedules"`
}

type SettingsHolder struct {
	Settings              *Settings
	Password              string
	AudioTransmitterToken uuid.UUID
}

func NewSettingsHolder() (*SettingsHolder, error) {
	f1, err := os.Open("/app/.env")
	if err != nil {
		log.Fatal(err)
	}
	defer f1.Close()

	f2, err := os.Open("/app/.env.local")
	if err != nil {
		log.Fatal(err)
	}
	defer f2.Close()

	settingsMap := make(map[string]string)

	s1 := bufio.NewScanner(f1)
	s2 := bufio.NewScanner(f2)

	for s1.Scan() {
		line := s1.Text()

		splitted := strings.SplitN(line, "=", 2)
		if len(splitted) == 2 {
			settingsMap[splitted[0]] = splitted[1]
		}
	}

	for s2.Scan() {
		line := s2.Text()

		splitted := strings.SplitN(line, "=", 2)
		if len(splitted) == 2 {
			settingsMap[splitted[0]] = splitted[1]
		}
	}

	settings, err := createSettings(settingsMap)
	if err != nil {
		return &SettingsHolder{}, err
	}

	s := &SettingsHolder{
		Settings:              settings,
		Password:              settingsMap["PASSWORD"],
		AudioTransmitterToken: uuid.New(),
	}

	s.WriteToFile()

	return s, nil
}

func (s *SettingsHolder) WriteToFile() error {
	f, err := os.OpenFile("/app/.env.local", os.O_RDWR|os.O_CREATE|os.O_TRUNC, 0755)
	if err != nil {
		return err
	}
	defer f.Close()

	writer := bufio.NewWriter(f)

	writer.WriteString(fmt.Sprintf("%s=%s\n", "OPENAI_API_KEY", s.Settings.OpenAIApiKey))
	writer.WriteString(fmt.Sprintf("%s=%s\n", "ALSA_INPUT", s.Settings.AlsaInput))
	writer.WriteString(fmt.Sprintf("%s=%d\n", "MIN_MIC_VOLUME", s.Settings.MinMicVolume))
	writer.WriteString(fmt.Sprintf("%s=%d\n", "MIN_SEG_VOLUME_DB", s.Settings.MinSegVolumeDb))
	writer.WriteString(fmt.Sprintf("%s=%d\n", "MIN_STEP_S", s.Settings.MinStepS))
	writer.WriteString(fmt.Sprintf("%s=%d\n", "MAX_STEP_S", s.Settings.MaxStepS))
	writer.WriteString(fmt.Sprintf("%s=%s\n", "COMMON_WORDS", strings.Join(s.Settings.CommonWords, ",")))
	writer.WriteString(fmt.Sprintf("%s=%s\n", "IGNORED_WORDS", strings.Join(s.Settings.IgnoredWords, ",")))
	writer.WriteString(fmt.Sprintf("%s=%s\n", "LANGUAGE", s.Settings.Language))
	writer.WriteString(fmt.Sprintf("%s=%s\n", "POST_PROCESSING_PROMPT", s.Settings.PostProcessingPrompt))
	writer.WriteString(fmt.Sprintf("%s=%t\n", "ENABLE_VAD", s.Settings.EnableVad))
	writer.WriteString(fmt.Sprintf("%s=%t\n", "SAVE_FILE", s.Settings.SaveFile))
	writer.WriteString(fmt.Sprintf("%s=%s\n", "WHISPER_VERSION", s.Settings.WhisperVersion))
	writer.WriteString(fmt.Sprintf("%s=%s\n", "CHATGPT_VERSION", s.Settings.ChatGPTVersion))
	writer.WriteString(fmt.Sprintf("%s=%s\n", "PASSWORD", s.Password))
	writer.WriteString(fmt.Sprintf("%s=%s\n", "AUDIOTRANSMITTER_TOKEN", s.AudioTransmitterToken.String()))
	writer.WriteString(fmt.Sprintf("%s=%t\n", "SAVE_TRANSLATION_CSV_LOG", s.Settings.SaveTranslationCsvLog))
	writer.WriteString(fmt.Sprintf("%s=%t\n", "DEV_MODE", s.Settings.DevMode))
	writer.WriteString(fmt.Sprintf("%s=%s\n", "PREEKTEKST_API_URL", s.Settings.PreektekstApiUrl))
	writer.WriteString(fmt.Sprintf("%s=%s\n", "PREEKTEKST_API_KEY", s.Settings.PreektekstApiKey))
	writer.WriteString(fmt.Sprintf("%s=%d\n", "PREEKTEKST_CHURCH_ID", s.Settings.PreektekstChurchID))
	writer.WriteString(fmt.Sprintf("%s=%t\n", "FINAL_TRANSCRIPTION_ONLY", s.Settings.FinalTranscriptionOnly))
	writer.WriteString(fmt.Sprintf("%s=%t\n", "AUTO_RESTART_ENABLED", s.Settings.AutoRestartEnabled))
	writer.WriteString(fmt.Sprintf("%s=%s\n", "AUTO_RESTART_TIME", s.Settings.AutoRestartTime))
	writer.WriteString(fmt.Sprintf("%s=%s\n", "AUTO_RESTART_DAYS", strings.Join(s.Settings.AutoRestartDays, ",")))
	writer.WriteString(fmt.Sprintf("%s=%t\n", "AUTO_UPLOAD_ENABLED", s.Settings.AutoUploadEnabled))

	// Serialize upload schedules as JSON
	uploadSchedulesJSON, _ := json.Marshal(s.Settings.AutoUploadSchedules)
	writer.WriteString(fmt.Sprintf("%s=%s\n", "AUTO_UPLOAD_SCHEDULES", string(uploadSchedulesJSON)))

	writer.Flush()
	return nil
}

func createSettings(newSettings map[string]string) (*Settings, error) {
	minMicVolume, err := strconv.Atoi(newSettings["MIN_MIC_VOLUME"])
	if err != nil {
		return nil, err
	}

	minSegVolumeDb, err := strconv.Atoi(newSettings["MIN_SEG_VOLUME_DB"])
	if err != nil {
		return nil, err
	}

	minStepS, err := strconv.Atoi(newSettings["MIN_STEP_S"])
	if err != nil {
		return nil, err
	}
	maxStepS, err := strconv.Atoi(newSettings["MAX_STEP_S"])
	if err != nil {
		return nil, err
	}

	enableVad, err := strconv.ParseBool(newSettings["ENABLE_VAD"])
	if err != nil {
		return nil, err
	}
	saveFile, err := strconv.ParseBool(newSettings["SAVE_FILE"])
	if err != nil {
		return nil, err
	}

	saveTranslationCsvLog, err := strconv.ParseBool(newSettings["SAVE_TRANSLATION_CSV_LOG"])
	if err != nil {
		return nil, err
	}

	devMode, err := strconv.ParseBool(newSettings["DEV_MODE"])
	if err != nil {
		return nil, err
	}

	churchID, err := strconv.ParseUint(newSettings["PREEKTEKST_CHURCH_ID"], 10, 32)
	if err != nil {
		return nil, err
	}

	finalTranscriptionOnly, err := strconv.ParseBool(newSettings["FINAL_TRANSCRIPTION_ONLY"])
	if err != nil {
		return nil, err
	}

	autoRestartEnabled, err := strconv.ParseBool(newSettings["AUTO_RESTART_ENABLED"])
	if err != nil {
		autoRestartEnabled = false // Default value
	}

	autoUploadEnabled, err := strconv.ParseBool(newSettings["AUTO_UPLOAD_ENABLED"])
	if err != nil {
		autoUploadEnabled = false // Default value
	}

	// Parse auto restart days
	autoRestartDays := []string{}
	if newSettings["AUTO_RESTART_DAYS"] != "" {
		autoRestartDays = strings.Split(newSettings["AUTO_RESTART_DAYS"], ",")
	}

	// Parse upload schedules from JSON
	var autoUploadSchedules []UploadSchedule
	if newSettings["AUTO_UPLOAD_SCHEDULES"] != "" {
		err = json.Unmarshal([]byte(newSettings["AUTO_UPLOAD_SCHEDULES"]), &autoUploadSchedules)
		if err != nil {
			autoUploadSchedules = []UploadSchedule{} // Default to empty array
		}
	}

	s := Settings{
		OpenAIApiKey:           newSettings["OPENAI_API_KEY"],
		AlsaInput:              newSettings["ALSA_INPUT"],
		MinMicVolume:           minMicVolume,
		MinSegVolumeDb:         minSegVolumeDb,
		MinStepS:               minStepS,
		MaxStepS:               maxStepS,
		IgnoredWords:           strings.Split(newSettings["IGNORED_WORDS"], ","),
		CommonWords:            strings.Split(newSettings["COMMON_WORDS"], ","),
		Language:               newSettings["LANGUAGE"],
		PostProcessingPrompt:   newSettings["POST_PROCESSING_PROMPT"],
		EnableVad:              enableVad,
		SaveFile:               saveFile,
		WhisperVersion:         newSettings["WHISPER_VERSION"],
		ChatGPTVersion:         newSettings["CHATGPT_VERSION"],
		SetupFinished:          len(newSettings["PASSWORD"]) > 0,
		SaveTranslationCsvLog:  saveTranslationCsvLog,
		DevMode:                devMode,
		PreektekstApiUrl:       newSettings["PREEKTEKST_API_URL"],
		PreektekstApiKey:       newSettings["PREEKTEKST_API_KEY"],
		PreektekstChurchID:     uint(churchID),
		FinalTranscriptionOnly: finalTranscriptionOnly,
		AutoRestartEnabled:     autoRestartEnabled,
		AutoRestartTime:        newSettings["AUTO_RESTART_TIME"],
		AutoRestartDays:        autoRestartDays,
		AutoUploadEnabled:      autoUploadEnabled,
		AutoUploadSchedules:    autoUploadSchedules,
	}

	return &s, nil
}
