package internal

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"sort"
	"strings"
	"time"

	"github.com/robfig/cron/v3"
)

type Scheduler struct {
	cron     *cron.Cron
	backend  *Backend
	cronJobs map[string]cron.EntryID // Track job IDs for removal
}

func NewScheduler(backend *Backend) *Scheduler {
	c := cron.New(cron.WithLocation(time.Local))

	s := &Scheduler{
		cron:     c,
		backend:  backend,
		cronJobs: make(map[string]cron.EntryID),
	}

	// Start the cron scheduler
	c.Start()

	// Initialize jobs based on current settings
	s.UpdateSchedules()

	return s
}

func (s *Scheduler) UpdateSchedules() {
	// Clear existing jobs
	s.clearAllJobs()

	settings := s.backend.SettingsHolder.Settings

	// Setup auto restart schedule
	if settings.AutoRestartEnabled && settings.AutoRestartTime != "" {
		s.setupAutoRestart(settings.AutoRestartTime, settings.AutoRestartDays)
	}

	// Setup auto upload schedules
	if settings.AutoUploadEnabled {
		s.setupAutoUploads(settings.AutoUploadSchedules)
	}
}

func (s *Scheduler) clearAllJobs() {
	for jobName, entryID := range s.cronJobs {
		s.cron.Remove(entryID)
		delete(s.cronJobs, jobName)
	}
}

func (s *Scheduler) setupAutoRestart(restartTime string, days []string) {
	if restartTime == "" {
		return
	}

	// Parse time (format: "HH:MM")
	timeParts := strings.Split(restartTime, ":")
	if len(timeParts) != 2 {
		log.Printf("Invalid restart time format: %s", restartTime)
		return
	}

	hour := timeParts[0]
	minute := timeParts[1]

	// If no specific days are set, restart daily
	if len(days) == 0 {
		cronExpr := fmt.Sprintf("%s %s * * *", minute, hour)
		entryID, err := s.cron.AddFunc(cronExpr, s.performRestart)
		if err != nil {
			log.Printf("Failed to schedule daily restart: %v", err)
			return
		}
		s.cronJobs["restart_daily"] = entryID
		log.Printf("Scheduled daily restart at %s", restartTime)
		return
	}

	// Schedule for specific days
	for _, day := range days {
		dayNum := s.dayNameToNumber(strings.TrimSpace(day))
		if dayNum == -1 {
			log.Printf("Invalid day name: %s", day)
			continue
		}

		cronExpr := fmt.Sprintf("%s %s * * %d", minute, hour, dayNum)
		entryID, err := s.cron.AddFunc(cronExpr, s.performRestart)
		if err != nil {
			log.Printf("Failed to schedule restart for %s: %v", day, err)
			continue
		}

		jobName := fmt.Sprintf("restart_%s", strings.ToLower(day))
		s.cronJobs[jobName] = entryID
		log.Printf("Scheduled restart for %s at %s", day, restartTime)
	}
}

func (s *Scheduler) setupAutoUploads(schedules []UploadSchedule) {
	for _, schedule := range schedules {
		if !schedule.Enabled || schedule.Time == "" || schedule.Day == "" {
			continue
		}

		// Parse time (format: "HH:MM")
		timeParts := strings.Split(schedule.Time, ":")
		if len(timeParts) != 2 {
			log.Printf("Invalid upload time format: %s", schedule.Time)
			continue
		}

		hour := timeParts[0]
		minute := timeParts[1]

		dayNum := s.dayNameToNumber(strings.TrimSpace(schedule.Day))
		if dayNum == -1 {
			log.Printf("Invalid day name: %s", schedule.Day)
			continue
		}

		cronExpr := fmt.Sprintf("%s %s * * %d", minute, hour, dayNum)
		entryID, err := s.cron.AddFunc(cronExpr, s.performAutoUpload)
		if err != nil {
			log.Printf("Failed to schedule upload for %s at %s: %v", schedule.Day, schedule.Time, err)
			continue
		}

		jobName := fmt.Sprintf("upload_%s_%s", strings.ToLower(schedule.Day), strings.ReplaceAll(schedule.Time, ":", ""))
		s.cronJobs[jobName] = entryID
		log.Printf("Scheduled auto upload for %s at %s", schedule.Day, schedule.Time)
	}
}

func (s *Scheduler) dayNameToNumber(dayName string) int {
	dayMap := map[string]int{
		"sunday":    0,
		"monday":    1,
		"tuesday":   2,
		"wednesday": 3,
		"thursday":  4,
		"friday":    5,
		"saturday":  6,
		"zondag":    0,
		"maandag":   1,
		"dinsdag":   2,
		"woensdag":  3,
		"donderdag": 4,
		"vrijdag":   5,
		"zaterdag":  6,
	}

	return dayMap[strings.ToLower(dayName)]
}

func (s *Scheduler) performRestart() {
	log.Println("Performing scheduled restart...")

	// Execute restart command
	cmd := exec.Command("docker", "compose", "restart")
	cmd.Dir = "/opt/kerktranscriptie"

	output, err := cmd.CombinedOutput()
	if err != nil {
		log.Printf("Failed to restart application: %v, output: %s", err, string(output))
		return
	}

	log.Printf("Application restarted successfully: %s", string(output))
}

func (s *Scheduler) performAutoUpload() {
	log.Println("Performing scheduled auto upload...")

	// Find the most recent recording file
	audioDir := "/app/audio"
	files, err := filepath.Glob(filepath.Join(audioDir, "*.tmp"))
	if err != nil {
		log.Printf("Failed to find audio files: %v", err)
		return
	}

	if len(files) == 0 {
		log.Println("No audio files found for auto upload")
		return
	}

	// Sort files by modification time (newest first)
	sort.Slice(files, func(i, j int) bool {
		infoI, errI := os.Stat(files[i])
		infoJ, errJ := os.Stat(files[j])
		if errI != nil || errJ != nil {
			return false
		}
		return infoI.ModTime().After(infoJ.ModTime())
	})

	// Get the most recent file
	mostRecentFile := files[0]

	// Extract filename without extension and path
	baseName := filepath.Base(mostRecentFile)
	fileName := strings.TrimSuffix(baseName, filepath.Ext(baseName))

	log.Printf("Auto uploading most recent file: %s", fileName)

	// Perform the upload using the existing preektekst functionality
	err = s.uploadToPreektekst(fileName)
	if err != nil {
		log.Printf("Failed to auto upload %s: %v", fileName, err)
		return
	}

	log.Printf("Successfully auto uploaded %s to Preektekst", fileName)
}

func (s *Scheduler) uploadToPreektekst(filename string) error {
	if s.backend.Preektekst == nil {
		return fmt.Errorf("Preektekst not configured")
	}

	inputFile := fmt.Sprintf("audio/%s.tmp", filename)
	outputFile := fmt.Sprintf("audio/%s.ogg", filename)

	// Convert to OGG format
	err := s.backend.Writer.convertToOgg(inputFile, outputFile)
	if err != nil {
		return fmt.Errorf("could not convert file: %w", err)
	}

	// Create transcription
	tID, err := s.backend.Preektekst.CreateNewTranscription(filename, s.backend.SettingsHolder.Settings.Language)
	if err != nil {
		os.Remove(outputFile)
		return fmt.Errorf("could not create transcription: %w", err)
	}

	// Upload audio
	err = s.backend.Preektekst.UploadTranscriptionAudio(tID, outputFile)
	if err != nil {
		return fmt.Errorf("could not upload audio: %w", err)
	}

	return nil
}

func (s *Scheduler) Stop() {
	if s.cron != nil {
		s.cron.Stop()
	}
}
